import z from 'zod';
import { PAYMENT_MODULE_INJECT_TOKENS } from 'src/shared';
import { Inject, Lifecycle, Provider } from '@heronjs/common';
import { IUseCase, UseCase, UseCaseContext } from '@cbidigital/aqua-ddd';
import { ICouponRepository } from '@features/payment/domain';
import { CreateGatewayCouponInput } from '@features/payment/domain/aggregates/coupon/types';
import { GatewayCouponStatusEnum } from '@features/payment/domain/aggregates/coupon/enums';
import { RuntimeError } from '@heronjs/common';

export type AddGatewayCouponUseCaseInput = {
    couponId: string;
    gateway: string;
    gatewayCouponId: string;
    status?: GatewayCouponStatusEnum;
};

export type AddGatewayCouponUseCaseOutput = { success: boolean };

const AddGatewayCouponUseCaseInputSchema = z.object({
    couponId: z.string().min(1, 'Coupon ID is required'),
    gateway: z.string().min(1, 'Gateway is required'),
    gatewayCouponId: z.string().min(1, 'Gateway coupon ID is required'),
    status: z.nativeEnum(GatewayCouponStatusEnum).optional(),
});

export type IAddGatewayCouponUseCase = IUseCase<
    AddGatewayCouponUseCaseInput,
    AddGatewayCouponUseCaseOutput,
    UseCaseContext
>;

@Provider({
    token: PAYMENT_MODULE_INJECT_TOKENS.USECASE.ADD_GATEWAY_COUPON,
    scope: Lifecycle.Transient,
})
export class AddGatewayCouponUseCase
    extends UseCase<AddGatewayCouponUseCaseInput, AddGatewayCouponUseCaseOutput, UseCaseContext>
    implements IAddGatewayCouponUseCase
{
    constructor(
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.REPOSITORY.COUPON)
        protected readonly repo: ICouponRepository,
    ) {
        super();
        this.setMethods(this.processing);
    }

    processing = async (input: AddGatewayCouponUseCaseInput) => {
        const { couponId, gateway, gatewayCouponId, status } = AddGatewayCouponUseCaseInputSchema.parse(input);

        // Find the coupon
        const coupon = await this.repo.findOne({
            filter: { id: { $eq: couponId } },
        });

        if (!coupon) {
            throw new RuntimeError('coupon', 30000, 'Coupon not found');
        }

        // Check if gateway coupon already exists
        const existingGatewayCoupon = coupon.getGatewayCoupon(gateway);
        if (existingGatewayCoupon) {
            throw new RuntimeError('coupon', 30001, `Gateway coupon for ${gateway} already exists`);
        }

        // Add gateway coupon
        const gatewayCouponInput: CreateGatewayCouponInput = {
            id: `${couponId}_${gateway}`,
            gateway,
            gatewayCouponId,
            status: status || GatewayCouponStatusEnum.ACTIVE,
        };

        coupon.addGatewayCoupon(gatewayCouponInput);

        // Save the updated coupon
        await this.repo.update(coupon);

        return { success: true };
    };
}
