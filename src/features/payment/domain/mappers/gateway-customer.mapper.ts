import { Lifecycle, Provider } from '@heronjs/common';
import { PAYMENT_MODULE_INJECT_TOKENS } from 'src/shared';
import { BaseMapper, IMapper } from '@cbidigital/aqua-ddd';
import { GatewayCustomerDto } from '@features/payment/domain/dtos';
import { GatewayCustomer, IGatewayCustomer } from '@features/payment/domain/aggregates/gateway-customer/gateway-customer';

export type IGatewayCustomerMapper = IMapper<GatewayCustomerDto, IGatewayCustomer>;

@Provider({
    token: PAYMENT_MODULE_INJECT_TOKENS.MAPPER.GATEWAY_CUSTOMER,
    scope: Lifecycle.Singleton,
})
export class GatewayCustomerMapper extends BaseMapper implements IGatewayCustomerMapper {
    constructor() {
        super();
    }

    async fromEntityToDto(entity: IGatewayCustomer): Promise<GatewayCustomerDto> {
        return {
            id: entity.id,
            gateway: entity.gateway,
            userId: entity.userId,
            gatewayCustomerId: entity.gatewayCustomerId,
            createdAt: entity.createdAt,
            updatedAt: entity.updatedAt,
        };
    }

    async fromDtoToEntity(dto: GatewayCustomerDto): Promise<IGatewayCustomer> {
        return new GatewayCustomer({
            id: dto.id,
            props: {
                id: dto.id,
                gateway: dto.gateway,
                userId: dto.userId,
                gatewayCustomerId: dto.gatewayCustomerId,
                createdAt: dto.createdAt,
                updatedAt: dto.updatedAt,
            },
        });
    }
}
