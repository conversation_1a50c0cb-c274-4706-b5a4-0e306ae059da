import { Lifecycle, Provider } from '@heronjs/common';
import { PAYMENT_MODULE_INJECT_TOKENS } from 'src/shared';
import { BaseMapper, IMapper } from '@cbidigital/aqua-ddd';
import { MethodDto } from '@features/payment/domain/dtos';
import { Method, IMethod } from '@features/payment/domain/aggregates/method/method';

export type IMethodMapper = IMapper<MethodDto, IMethod>;

@Provider({
    token: PAYMENT_MODULE_INJECT_TOKENS.MAPPER.METHOD,
    scope: Lifecycle.Singleton,
})
export class MethodMapper extends BaseMapper implements IMethodMapper {
    constructor() {
        super();
    }

    async fromEntityToDto(entity: IMethod): Promise<MethodDto> {
        return {
            code: entity.code,
            gateway: entity.gateway,
            label: entity.label,
            desc: entity.desc,
            status: entity.status,
            visibility: entity.visibility,
            sortOrder: entity.sortOrder,
            platformExclusion: entity.platformExclusion,
            sourceTypeExclusion: entity.sourceTypeExclusion,
            createdAt: entity.createdAt,
            updatedAt: entity.updatedAt,
        };
    }

    async fromDtoToEntity(dto: MethodDto): Promise<IMethod> {
        return new Method({
            id: dto.code,
            props: {
                code: dto.code,
                gateway: dto.gateway,
                label: dto.label,
                desc: dto.desc,
                status: dto.status,
                visibility: dto.visibility,
                sortOrder: dto.sortOrder,
                platformExclusion: dto.platformExclusion,
                sourceTypeExclusion: dto.sourceTypeExclusion,
                createdAt: dto.createdAt,
                updatedAt: dto.updatedAt,
            },
        });
    }
}
