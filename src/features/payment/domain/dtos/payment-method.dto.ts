import { Nullable } from '@heronjs/common';
import { GatewayCodesEnum } from '@features/payment/domain/aggregates/gateway/enums';
import { PaymentMethodTypeEnum, PaymentMethodFundingEnum } from '@features/payment/domain/aggregates/payment-method/enums';

export type PaymentMethodDto = {
    id: string;
    gateway: GatewayCodesEnum;
    last4: string;
    label: string;
    reference: string;
    isDefault: boolean;
    userId: string;
    type: PaymentMethodTypeEnum;
    expMonth: string;
    expYear: string;
    funding: PaymentMethodFundingEnum;
    tenantId: string;
    archived: boolean;
    createdAt: Date;
    updatedAt: Nullable<Date>;
};
