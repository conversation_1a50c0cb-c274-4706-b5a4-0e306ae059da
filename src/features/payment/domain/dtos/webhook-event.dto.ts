import { Nullable } from '@heronjs/common';
import { WebhookEventStatusEnum } from '@features/payment/domain/aggregates/webhook-event/enums';

export type WebhookEventDto = {
    id: string;
    gateway: string;
    referenceId: string;
    eventId: string;
    eventType: string;
    status: WebhookEventStatusEnum;
    payload: Record<string, any>;
    errorMessage: Nullable<string>;
    createdAt: Date;
    updatedAt: Nullable<Date>;
};
