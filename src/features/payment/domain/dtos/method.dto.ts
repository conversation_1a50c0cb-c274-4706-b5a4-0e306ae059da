import { Nullable } from '@heronjs/common';
import { GatewayCodesEnum } from '@features/payment/domain/aggregates/gateway/enums';
import { MethodStatusEnum } from '@features/payment/domain/aggregates/method/enums';

export type MethodDto = {
    code: string;
    gateway: GatewayCodesEnum;
    label: string;
    desc: Nullable<string>;
    status: MethodStatusEnum;
    visibility: boolean;
    sortOrder: number;
    platformExclusion: Nullable<any>;
    sourceTypeExclusion: Nullable<any>;
    createdAt: Date;
    updatedAt: Nullable<Date>;
};
