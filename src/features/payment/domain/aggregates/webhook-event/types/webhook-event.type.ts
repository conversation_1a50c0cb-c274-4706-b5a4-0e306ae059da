import { Nullable } from '@heronjs/common';
import { WebhookEventStatusEnum } from '@features/payment/domain/aggregates/webhook-event/enums';

export type CreateWebhookEventInput = {
    id: string;
    gateway: string;
    referenceId: string;
    eventId: string;
    eventType: string;
    status: WebhookEventStatusEnum;
    payload: Record<string, any>;
    errorMessage?: Nullable<string>;
};

export type UpdateWebhookEventInput = {
    status?: WebhookEventStatusEnum;
    payload?: Record<string, any>;
    errorMessage?: Nullable<string>;
};
