import { Nullable } from '@heronjs/common';
import { AggregateRoot, AggregateRootConstructorPayload, IAggregateRoot } from '@cbidigital/aqua-ddd';
import { WebhookEventStatusEnum } from '@features/payment/domain/aggregates/webhook-event/enums';
import {
    CreateWebhookEventInput,
    UpdateWebhookEventInput,
} from '@features/payment/domain/aggregates/webhook-event/types';

export type WebhookEventProps = {
    id: string;
    gateway: string;
    referenceId: string;
    eventId: string;
    eventType: string;
    status: WebhookEventStatusEnum;
    payload: Record<string, any>;
    errorMessage: Nullable<string>;
    createdAt: Date;
    updatedAt: Nullable<Date>;
};

export type WebhookEventMethods = {
    createWebhookEvent(payload: CreateWebhookEventInput): Promise<void>;
    updateWebhookEvent(payload: UpdateWebhookEventInput): void;
    markAsProcessing(): void;
    markAsProcessed(): void;
    markAsFailed(errorMessage: string): void;
    markAsRetrying(): void;
    markAsIgnored(): void;
    updatePayload(payload: Record<string, any>): void;
};

export type IWebhookEvent = IAggregateRoot<WebhookEventProps, WebhookEventMethods>;

export class WebhookEvent extends AggregateRoot<WebhookEventProps, WebhookEventMethods> implements IWebhookEvent {
    static AGGREGATE_NAME = 'webhook-event';

    constructor(payload: AggregateRootConstructorPayload<WebhookEventProps>) {
        super(payload);
    }

    /** Properties **/

    get id(): string {
        return this.props.id;
    }

    get gateway(): string {
        return this.props.gateway;
    }

    get referenceId(): string {
        return this.props.referenceId;
    }

    get eventId(): string {
        return this.props.eventId;
    }

    get eventType(): string {
        return this.props.eventType;
    }

    get status(): WebhookEventStatusEnum {
        return this.props.status;
    }

    get payload(): Record<string, any> {
        return this.props.payload;
    }

    get errorMessage(): Nullable<string> {
        return this.props.errorMessage;
    }

    get createdAt(): Date {
        return this.props.createdAt;
    }

    get updatedAt(): Nullable<Date> {
        return this.props.updatedAt;
    }

    /** Methods **/

    protected setId(payload?: string): void {
        if (payload !== undefined) this.setProp('id', payload);
    }

    private setGateway(payload?: string): void {
        if (payload !== undefined) this.setProp('gateway', payload);
    }

    private setReferenceId(payload?: string): void {
        if (payload !== undefined) this.setProp('referenceId', payload);
    }

    private setEventId(payload?: string): void {
        if (payload !== undefined) this.setProp('eventId', payload);
    }

    private setEventType(payload?: string): void {
        if (payload !== undefined) this.setProp('eventType', payload);
    }

    private setStatus(payload?: WebhookEventStatusEnum): void {
        if (payload !== undefined) this.setProp('status', payload);
    }

    private setPayload(payload?: Record<string, any>): void {
        if (payload !== undefined) this.setProp('payload', payload);
    }

    private setErrorMessage(payload?: Nullable<string>): void {
        if (payload !== undefined) this.setProp('errorMessage', payload);
    }

    private setCreatedAt(payload?: Date): void {
        if (payload !== undefined) this.setProp('createdAt', payload);
    }

    private setUpdatedAt(payload?: Date): void {
        if (payload !== undefined) this.setProp('updatedAt', payload);
    }

    async createWebhookEvent(payload: CreateWebhookEventInput) {
        this.setId(payload.id);
        this.setGateway(payload.gateway);
        this.setReferenceId(payload.referenceId);
        this.setEventId(payload.eventId);
        this.setEventType(payload.eventType);
        this.setStatus(payload.status);
        this.setPayload(payload.payload);
        this.setErrorMessage(payload.errorMessage || null);
        this.setCreatedAt(new Date());
    }

    updateWebhookEvent(payload: UpdateWebhookEventInput): void {
        this.setStatus(payload.status);
        this.setPayload(payload.payload);
        this.setErrorMessage(payload.errorMessage);
        this.setUpdatedAt(new Date());
    }

    markAsProcessing(): void {
        this.setStatus(WebhookEventStatusEnum.PROCESSING);
        this.setErrorMessage(null);
        this.setUpdatedAt(new Date());
    }

    markAsProcessed(): void {
        this.setStatus(WebhookEventStatusEnum.PROCESSED);
        this.setErrorMessage(null);
        this.setUpdatedAt(new Date());
    }

    markAsFailed(errorMessage: string): void {
        this.setStatus(WebhookEventStatusEnum.FAILED);
        this.setErrorMessage(errorMessage);
        this.setUpdatedAt(new Date());
    }

    markAsRetrying(): void {
        this.setStatus(WebhookEventStatusEnum.RETRYING);
        this.setUpdatedAt(new Date());
    }

    markAsIgnored(): void {
        this.setStatus(WebhookEventStatusEnum.IGNORED);
        this.setUpdatedAt(new Date());
    }

    updatePayload(payload: Record<string, any>): void {
        this.setPayload(payload);
        this.setUpdatedAt(new Date());
    }

    isPending(): boolean {
        return this.status === WebhookEventStatusEnum.PENDING;
    }

    isProcessing(): boolean {
        return this.status === WebhookEventStatusEnum.PROCESSING;
    }

    isProcessed(): boolean {
        return this.status === WebhookEventStatusEnum.PROCESSED;
    }

    isFailed(): boolean {
        return this.status === WebhookEventStatusEnum.FAILED;
    }

    isRetrying(): boolean {
        return this.status === WebhookEventStatusEnum.RETRYING;
    }

    isIgnored(): boolean {
        return this.status === WebhookEventStatusEnum.IGNORED;
    }
}
