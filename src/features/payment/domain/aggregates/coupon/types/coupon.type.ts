import { Nullable } from '@heronjs/common';
import {
    CouponStatusEnum,
    CouponDurationEnum,
    GatewayCouponStatusEnum,
} from '@features/payment/domain/aggregates/coupon/enums';

export type CreateCouponInput = {
    id: string;
    name: string;
    code: string;
    status?: Nullable<CouponStatusEnum>;
    duration?: Nullable<CouponDurationEnum>;
    percentOff?: Nullable<number>;
    amountOff?: Nullable<number>;
    effectTo?: Nullable<number>;
    maxRedemptions?: Nullable<number>;
    currency?: Nullable<string>;
};

export type UpdateCouponInput = {
    name?: string;
    code?: string;
    status?: Nullable<CouponStatusEnum>;
    duration?: Nullable<CouponDurationEnum>;
    percentOff?: Nullable<number>;
    amountOff?: Nullable<number>;
    effectTo?: Nullable<number>;
    maxRedemptions?: Nullable<number>;
    currency?: Nullable<string>;
};

export type CreateGatewayCouponInput = {
    id: string;
    gateway: string;
    gatewayCouponId: string;
    status: GatewayCouponStatusEnum;
};

export type UpdateGatewayCouponInput = {
    gatewayCouponId?: string;
    status?: GatewayCouponStatusEnum;
};
