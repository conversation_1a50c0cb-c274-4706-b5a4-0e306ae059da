import { Nullable } from '@heronjs/common';
import { Entity, EntityConstructorPayload, IEntity } from '@cbidigital/aqua-ddd';
import { GatewayCouponStatusEnum } from '@features/payment/domain/aggregates/coupon/enums';

export type GatewayCouponProps = {
    id: string;
    gateway: string;
    gatewayCouponId: string;
    status: GatewayCouponStatusEnum;
    createdAt: Date;
    updatedAt: Nullable<Date>;
};

export type GatewayCouponMethods = {
    markAsSynced(): void;
    markAsFailed(): void;
    updateGatewayCouponId(gatewayCouponId: string): void;
};

export type IGatewayCoupon = IEntity<GatewayCouponProps, GatewayCouponMethods>;

export class GatewayCoupon
    extends Entity<GatewayCouponProps, GatewayCouponMethods>
    implements IGatewayCoupon
{
    constructor(payload: EntityConstructorPayload<GatewayCouponProps>) {
        super(payload);
    }

    /** Properties **/

    get id(): string {
        return this.props.id;
    }

    get gateway(): string {
        return this.props.gateway;
    }

    get gatewayCouponId(): string {
        return this.props.gatewayCouponId;
    }

    get status(): GatewayCouponStatusEnum {
        return this.props.status;
    }

    get createdAt(): Date {
        return this.props.createdAt;
    }

    get updatedAt(): Nullable<Date> {
        return this.props.updatedAt;
    }

    /** Methods **/

    private setGatewayCouponId(payload?: string): void {
        if (payload !== undefined) this.setProp('gatewayCouponId', payload);
    }

    private setStatus(payload?: GatewayCouponStatusEnum): void {
        if (payload !== undefined) this.setProp('status', payload);
    }

    private setUpdatedAt(payload?: Date): void {
        if (payload !== undefined) this.setProp('updatedAt', payload);
    }

    markAsSynced(): void {
        this.setStatus(GatewayCouponStatusEnum.SYNCED);
        this.setUpdatedAt(new Date());
    }

    markAsFailed(): void {
        this.setStatus(GatewayCouponStatusEnum.FAILED);
        this.setUpdatedAt(new Date());
    }

    updateGatewayCouponId(gatewayCouponId: string): void {
        this.setGatewayCouponId(gatewayCouponId);
        this.setUpdatedAt(new Date());
    }

    isActive(): boolean {
        return (
            this.status === GatewayCouponStatusEnum.ACTIVE || this.status === GatewayCouponStatusEnum.SYNCED
        );
    }
}
