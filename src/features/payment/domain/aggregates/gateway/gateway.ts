import { Nullable } from '@heronjs/common';
import { AggregateRoot, AggregateRootConstructorPayload, IAggregateRoot } from '@cbidigital/aqua-ddd';
import { GatewayCodesEnum, GatewayStatusEnum } from '@features/payment/domain/aggregates/gateway/enums';
import { CreateGatewayInput, UpdateGatewayInput } from '@features/payment/domain/aggregates/gateway/types';

export type GatewayProps = {
    code: GatewayCodesEnum;
    label: string;
    desc: Nullable<string>;
    status: GatewayStatusEnum;
    createdAt: Date;
    updatedAt: Nullable<Date>;
};

export type GatewayMethods = {
    createGateway(payload: CreateGatewayInput): Promise<void>;
    updateGateway(payload: UpdateGatewayInput): void;
};

export type IGateway = IAggregateRoot<GatewayProps, GatewayMethods>;
export class Gateway extends AggregateRoot<GatewayProps, GatewayMethods> implements IGateway {
    static AGGREGATE_NAME = 'membership';

    constructor(payload: AggregateRootConstructorPayload<GatewayProps>) {
        super(payload);
    }

    /** Properties **/

    get code(): GatewayCodesEnum {
        return this.props.code;
    }

    get label(): string {
        return this.props.label;
    }

    get desc(): Nullable<string> {
        return this.props.desc;
    }

    get status(): GatewayStatusEnum {
        return this.props.status;
    }

    get createdAt(): Date {
        return this.props.createdAt;
    }

    get updatedAt(): Nullable<Date> {
        return this.props.updatedAt;
    }

    /** Methods **/

    private setCode(payload?: GatewayCodesEnum): void {
        if (payload !== undefined) this.setProp('code', payload);
    }

    private setLabel(payload?: string): void {
        if (payload !== undefined) this.setProp('label', payload);
    }

    private setDesc(payload?: Nullable<string>): void {
        if (payload !== undefined) this.setProp('desc', payload);
    }

    private setStatus(payload?: GatewayStatusEnum): void {
        if (payload !== undefined) this.setProp('status', payload);
    }

    private setCreatedAt(payload?: Date): void {
        if (payload !== undefined) this.setProp('createdAt', payload);
    }

    private setUpdatedAt(payload?: Date): void {
        if (payload !== undefined) this.setProp('updatedAt', payload);
    }

    async createGateway(payload: CreateGatewayInput) {
        this.setCode(payload.code);
        this.setLabel(payload.label);
        this.setDesc(payload.desc);
        this.setStatus(payload.status);
        this.setCreatedAt(new Date());
    }

    updateGateway(payload: any): void {
        this.setLabel(payload.label);
        this.setDesc(payload.desc);
        this.setStatus(payload.status);
        this.setUpdatedAt(new Date());
    }
}
