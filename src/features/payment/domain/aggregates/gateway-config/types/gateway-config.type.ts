import { Nullable } from '@heronjs/common';
import { GatewayCodesEnum } from '@features/payment/domain/aggregates/gateway/enums';

export type CreateGatewayConfigInput = {
    code: string;
    gatewayCode: GatewayCodesEnum;
    label: string;
    desc?: Nullable<string>;
    value: string;
};

export type UpdateGatewayConfigInput = {
    label?: string;
    desc?: Nullable<string>;
    value?: string;
};
