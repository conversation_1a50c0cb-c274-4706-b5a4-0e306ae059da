import { TransactionStatusEnum } from '@features/payment/domain/aggregates/transaction/enums';

export type CreateTransactionInput = {
    id: string;
    gateway: string;
    userId: string;
    gatewayTransactionId: string;
    status: TransactionStatusEnum;
};

export type UpdateTransactionInput = {
    gatewayTransactionId?: string;
    status?: TransactionStatusEnum;
};

export type CreateTransactionLogInput = {
    id: string;
    status: TransactionStatusEnum;
};

export type UpdateTransactionLogInput = {
    status?: TransactionStatusEnum;
};
