import { Nullable } from '@heronjs/common';
import { TransactionLog, ITransactionLog } from '@features/payment/domain/aggregates/transaction/entities';
import { AggregateRoot, AggregateRootConstructorPayload, IAggregateRoot } from '@cbidigital/aqua-ddd';
import { TransactionStatusEnum } from '@features/payment/domain/aggregates/transaction/enums';
import {
    CreateTransactionInput,
    UpdateTransactionInput,
    CreateTransactionLogInput,
    UpdateTransactionLogInput,
} from '@features/payment/domain/aggregates/transaction/types';

export type TransactionProps = {
    id: string;
    gateway: string;
    userId: string;
    gatewayTransactionId: string;
    status: TransactionStatusEnum;
    transactionLogs: ITransactionLog[];
    createdAt: Date;
    updatedAt: Nullable<Date>;
};

export type TransactionMethods = {
    createTransaction(payload: CreateTransactionInput): Promise<void>;
    updateTransaction(payload: UpdateTransactionInput): void;
    updateStatus(status: TransactionStatusEnum): void;
    addTransactionLog(payload: CreateTransactionLogInput): void;
    updateTransactionLog(logId: string, payload: UpdateTransactionLogInput): void;
    getTransactionLog(logId: string): ITransactionLog;
    getLatestTransactionLog(): Nullable<ITransactionLog>;
};

export type ITransaction = IAggregateRoot<TransactionProps, TransactionMethods>;

export class Transaction extends AggregateRoot<TransactionProps, TransactionMethods> implements ITransaction {
    static AGGREGATE_NAME = 'transaction';

    constructor(payload: AggregateRootConstructorPayload<TransactionProps>) {
        super(payload);
    }

    /** Properties **/

    get id(): string {
        return this.props.id;
    }

    get gateway(): string {
        return this.props.gateway;
    }

    get userId(): string {
        return this.props.userId;
    }

    get gatewayTransactionId(): string {
        return this.props.gatewayTransactionId;
    }

    get status(): TransactionStatusEnum {
        return this.props.status;
    }

    get transactionLogs(): ITransactionLog[] {
        return this.props.transactionLogs;
    }

    get createdAt(): Date {
        return this.props.createdAt;
    }

    get updatedAt(): Nullable<Date> {
        return this.props.updatedAt;
    }

    /** Methods **/

    protected setId(payload?: string): void {
        if (payload !== undefined) this.setProp('id', payload);
    }

    private setGateway(payload?: string): void {
        if (payload !== undefined) this.setProp('gateway', payload);
    }

    private setUserId(payload?: string): void {
        if (payload !== undefined) this.setProp('userId', payload);
    }

    private setGatewayTransactionId(payload?: string): void {
        if (payload !== undefined) this.setProp('gatewayTransactionId', payload);
    }

    private setStatus(payload?: TransactionStatusEnum): void {
        if (payload !== undefined) this.setProp('status', payload);
    }

    private setTransactionLogs(payload?: ITransactionLog[]): void {
        if (payload !== undefined) this.setProp('transactionLogs', payload);
    }

    private setCreatedAt(payload?: Date): void {
        if (payload !== undefined) this.setProp('createdAt', payload);
    }

    private setUpdatedAt(payload?: Date): void {
        if (payload !== undefined) this.setProp('updatedAt', payload);
    }

    async createTransaction(payload: CreateTransactionInput) {
        this.setId(payload.id);
        this.setGateway(payload.gateway);
        this.setUserId(payload.userId);
        this.setGatewayTransactionId(payload.gatewayTransactionId);
        this.setStatus(payload.status);
        this.setTransactionLogs([]);
        this.setCreatedAt(new Date());
    }

    updateTransaction(payload: UpdateTransactionInput): void {
        this.setGatewayTransactionId(payload.gatewayTransactionId);
        this.setStatus(payload.status);
        this.setUpdatedAt(new Date());
    }

    updateStatus(status: TransactionStatusEnum): void {
        this.setStatus(status);
        this.setUpdatedAt(new Date());
        
        // Add a transaction log entry for the status change
        this.addTransactionLog({
            id: `${this.id}-log-${Date.now()}`,
            status: status,
        });
    }

    addTransactionLog(payload: CreateTransactionLogInput): void {
        const transactionLog = new TransactionLog({
            id: payload.id,
            props: {
                id: payload.id,
                transactionId: this.id,
                status: payload.status,
                createdAt: new Date(),
                updatedAt: null,
            },
        });

        const updatedTransactionLogs = [...this.transactionLogs, transactionLog];
        this.setTransactionLogs(updatedTransactionLogs);
        this.setUpdatedAt(new Date());
    }

    updateTransactionLog(logId: string, payload: UpdateTransactionLogInput): void {
        const transactionLog = this.getTransactionLog(logId);
        if (!transactionLog) {
            throw new Error(`Transaction log with id ${logId} not found`);
        }

        if (payload.status) {
            transactionLog.updateStatus(payload.status);
        }

        this.setUpdatedAt(new Date());
    }

    getTransactionLog(logId: string): ITransactionLog {
        const transactionLog = this.transactionLogs.find((log) => log.id === logId);
        if (!transactionLog) {
            throw new Error(`Transaction log with id ${logId} not found`);
        }
        return transactionLog;
    }

    getLatestTransactionLog(): Nullable<ITransactionLog> {
        if (this.transactionLogs.length === 0) {
            return null;
        }
        
        return this.transactionLogs.reduce((latest, current) => {
            return current.createdAt > latest.createdAt ? current : latest;
        });
    }
}
