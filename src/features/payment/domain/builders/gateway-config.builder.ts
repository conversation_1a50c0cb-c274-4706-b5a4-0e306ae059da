import {
    AggregateRootBuilder,
    IAggregateRootBuilder,
    AggregateRootBuilderPayload,
} from '@cbidigital/aqua-ddd';
import { Lifecycle, Provider } from '@heronjs/common';
import { PAYMENT_MODULE_INJECT_TOKENS } from 'src/shared';
import { GatewayConfig, IGatewayConfig } from '@features/payment/domain/aggregates/gateway-config/gateway-config';

export type GatewayConfigBuilderBuildPayload = AggregateRootBuilderPayload<IGatewayConfig>;
export type IGatewayConfigBuilder = IAggregateRootBuilder<IGatewayConfig>;

@Provider({
    token: PAYMENT_MODULE_INJECT_TOKENS.BUILDER.GATEWAY_CONFIG,
    scope: Lifecycle.Singleton,
})
export class GatewayConfigBuilder extends AggregateRootBuilder<IGatewayConfig> implements IGatewayConfigBuilder {
    async build({ id, props, externalProps }: GatewayConfigBuilderBuildPayload = {}): Promise<IGatewayConfig> {
        return new GatewayConfig({ id, props, externalProps });
    }
}
