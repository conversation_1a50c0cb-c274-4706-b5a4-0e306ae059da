import {
    AggregateRootBuilder,
    IAggregateRootBuilder,
    AggregateRootBuilderPayload,
} from '@cbidigital/aqua-ddd';
import { Lifecycle, Provider } from '@heronjs/common';
import { PAYMENT_MODULE_INJECT_TOKENS } from 'src/shared';
import { Gateway, IGateway } from '@features/payment/domain/aggregates/gateway/gateway';

export type GatewayBuilderBuildPayload = AggregateRootBuilderPayload<IGateway>;
export type IGatewayBuilder = IAggregateRootBuilder<IGateway>;

@Provider({
    token: PAYMENT_MODULE_INJECT_TOKENS.BUILDER.GATEWAY,
    scope: Lifecycle.Singleton,
})
export class GatewayBuilder extends AggregateRootBuilder<IGateway> implements IGatewayBuilder {
    async build({ id, props, externalProps }: GatewayBuilderBuildPayload = {}): Promise<IGateway> {
        return new Gateway({ id, props, externalProps });
    }
}
