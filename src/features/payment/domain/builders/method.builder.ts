import {
    AggregateRootBuilder,
    IAggregateRootBuilder,
    AggregateRootBuilderPayload,
} from '@cbidigital/aqua-ddd';
import { Lifecycle, Provider } from '@heronjs/common';
import { PAYMENT_MODULE_INJECT_TOKENS } from 'src/shared';
import { Method, IMethod } from '@features/payment/domain/aggregates/method/method';

export type MethodBuilderBuildPayload = AggregateRootBuilderPayload<IMethod>;
export type IMethodBuilder = IAggregateRootBuilder<IMethod>;

@Provider({
    token: PAYMENT_MODULE_INJECT_TOKENS.BUILDER.METHOD,
    scope: Lifecycle.Singleton,
})
export class MethodBuilder extends AggregateRootBuilder<IMethod> implements IMethodBuilder {
    async build({ id, props, externalProps }: MethodBuilderBuildPayload = {}): Promise<IMethod> {
        return new Method({ id, props, externalProps });
    }
}
