import {
    AggregateRootBuilder,
    IAggregateRootBuilder,
    AggregateRootBuilderPayload,
} from '@cbidigital/aqua-ddd';
import { Lifecycle, Provider } from '@heronjs/common';
import { PAYMENT_MODULE_INJECT_TOKENS } from 'src/shared';
import { Coupon, ICoupon } from '@features/payment/domain/aggregates/coupon/coupon';

export type CouponBuilderBuildPayload = AggregateRootBuilderPayload<ICoupon>;
export type ICouponBuilder = IAggregateRootBuilder<ICoupon>;

@Provider({
    token: PAYMENT_MODULE_INJECT_TOKENS.BUILDER.COUPON,
    scope: Lifecycle.Singleton,
})
export class CouponBuilder extends AggregateRootBuilder<ICoupon> implements ICouponBuilder {
    async build({ id, props, externalProps }: CouponBuilderBuildPayload = {}): Promise<ICoupon> {
        return new Coupon({ id, props, externalProps });
    }
}
