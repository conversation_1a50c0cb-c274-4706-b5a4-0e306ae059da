import { PAYMENT_MODULE_INJECT_TOKENS } from 'src/shared';
import { IPaymentMethodDao } from '@features/payment/infra/database';
import { IPaymentMethodRepository } from '@features/payment/domain/repositories';
import { IPaymentMethod } from '@features/payment/domain/aggregates/payment-method/payment-method';
import { Inject, Optional, Lifecycle, Repository, DataSource } from '@heronjs/common';
import {
    IDatabase,
    QueryInput,
    BaseRepository,
    QueryInputFindOne,
    RepositoryOptions,
} from '@cbidigital/aqua-ddd';
import { PaymentMethodDto, IPaymentMethodMapper } from '@features/payment/domain';

@Repository({
    token: PAYMENT_MODULE_INJECT_TOKENS.REPOSITORY.PAYMENT_METHOD,
    scope: Lifecycle.Singleton,
})
export class PaymentMethodRepository extends BaseRepository<IPaymentMethod> implements IPaymentMethodRepository {
    constructor(
        @DataSource() protected readonly db: IDatabase,
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.DAO.PAYMENT_METHOD)
        private readonly paymentMethodDao: IPaymentMethodDao,
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.MAPPER.PAYMENT_METHOD)
        private readonly mapper: IPaymentMethodMapper,
    ) {
        super({ db });
    }

    async create(entity: IPaymentMethod, options?: RepositoryOptions): Promise<IPaymentMethod> {
        const trx = options?.trx ?? (await this.db.startTrx(options));
        const dto = await this.mapper.fromEntityToDto(entity);
        await this.paymentMethodDao.create(dto, options);
        if (!options?.trx) await trx.commit();

        return entity;
    }

    async update(entity: IPaymentMethod, options?: RepositoryOptions): Promise<IPaymentMethod> {
        const trx = options?.trx ?? (await this.db.startTrx(options));
        const dto = await this.mapper.fromEntityToDto(entity);
        await this.paymentMethodDao.update(dto, { ...options, trx });
        if (!options?.trx) await trx.commit();

        return entity;
    }

    async delete(entity: IPaymentMethod, options?: RepositoryOptions): Promise<IPaymentMethod> {
        await this.withTransaction(async (trx) => {
            await this.paymentMethodDao.deleteById(entity.id, { ...options, trx });
            return entity;
        }, options);

        return entity;
    }

    async find(input: QueryInput, options?: RepositoryOptions): Promise<IPaymentMethod[]> {
        const dtos = await this.paymentMethodDao.find(input, options);
        const entities = await this.mapper.fromDtosToEntities(dtos as PaymentMethodDto[]);
        return entities;
    }

    async findOne(input: QueryInputFindOne, options?: RepositoryOptions): Promise<Optional<IPaymentMethod>> {
        const dto = await this.paymentMethodDao.findOne(input, options);
        const entity = dto ? await this.mapper.fromDtoToEntity(dto as PaymentMethodDto) : dto;
        return entity;
    }

    async upsertList(entities: IPaymentMethod[], options: RepositoryOptions) {
        const dtos = await this.mapper.fromEntitiesToDtos(entities);
        return entities;
    }

    async count(input: Pick<QueryInput, 'filter'>, options?: RepositoryOptions): Promise<number> {
        return this.paymentMethodDao.count(input, options);
    }
}
