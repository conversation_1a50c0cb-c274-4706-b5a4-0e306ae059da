import { PAYMENT_MODULE_INJECT_TOKENS } from 'src/shared';
import { IGatewayConfigDao } from '@features/payment/infra/database';
import { IGatewayConfigRepository } from '@features/payment/domain/repositories';
import { IGatewayConfig } from '@features/payment/domain/aggregates/gateway-config/gateway-config';
import { Inject, Optional, Lifecycle, Repository, DataSource } from '@heronjs/common';
import {
    IDatabase,
    QueryInput,
    BaseRepository,
    QueryInputFindOne,
    RepositoryOptions,
} from '@cbidigital/aqua-ddd';
import { GatewayConfigDto, IGatewayConfigMapper } from '@features/payment/domain';

@Repository({
    token: PAYMENT_MODULE_INJECT_TOKENS.REPOSITORY.GATEWAY_CONFIG,
    scope: Lifecycle.Singleton,
})
export class GatewayConfigRepository extends BaseRepository<IGatewayConfig> implements IGatewayConfigRepository {
    constructor(
        @DataSource() protected readonly db: IDatabase,
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.DAO.GATEWAY_CONFIG)
        private readonly gatewayConfigDao: IGatewayConfigDao,
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.MAPPER.GATEWAY_CONFIG)
        private readonly mapper: IGatewayConfigMapper,
    ) {
        super({ db });
    }

    async create(entity: IGatewayConfig, options?: RepositoryOptions): Promise<IGatewayConfig> {
        const trx = options?.trx ?? (await this.db.startTrx(options));
        const dto = await this.mapper.fromEntityToDto(entity);
        await this.gatewayConfigDao.create(dto, options);
        if (!options?.trx) await trx.commit();

        return entity;
    }

    async update(entity: IGatewayConfig, options?: RepositoryOptions): Promise<IGatewayConfig> {
        const trx = options?.trx ?? (await this.db.startTrx(options));
        const dto = await this.mapper.fromEntityToDto(entity);
        await this.gatewayConfigDao.update(dto, { ...options, trx });
        if (!options?.trx) await trx.commit();

        return entity;
    }

    async delete(entity: IGatewayConfig, options?: RepositoryOptions): Promise<IGatewayConfig> {
        await this.withTransaction(async (trx) => {
            await this.gatewayConfigDao.deleteById(entity.id, { ...options, trx });
            return entity;
        }, options);

        return entity;
    }

    async find(input: QueryInput, options?: RepositoryOptions): Promise<IGatewayConfig[]> {
        const dtos = await this.gatewayConfigDao.find(input, options);
        const entities = await this.mapper.fromDtosToEntities(dtos as GatewayConfigDto[]);
        return entities;
    }

    async findOne(input: QueryInputFindOne, options?: RepositoryOptions): Promise<Optional<IGatewayConfig>> {
        const dto = await this.gatewayConfigDao.findOne(input, options);
        const entity = dto ? await this.mapper.fromDtoToEntity(dto as GatewayConfigDto) : dto;
        return entity;
    }

    async upsertList(entities: IGatewayConfig[], options: RepositoryOptions) {
        const dtos = await this.mapper.fromEntitiesToDtos(entities);
        return entities;
    }

    async count(input: Pick<QueryInput, 'filter'>, options?: RepositoryOptions): Promise<number> {
        return this.gatewayConfigDao.count(input, options);
    }
}
