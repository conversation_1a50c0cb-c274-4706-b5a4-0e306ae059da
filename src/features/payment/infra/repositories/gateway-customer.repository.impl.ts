import { PAYMENT_MODULE_INJECT_TOKENS } from 'src/shared';
import { IGatewayCustomerDao } from '@features/payment/infra/database';
import { IGatewayCustomerRepository } from '@features/payment/domain/repositories';
import { IGatewayCustomer } from '@features/payment/domain/aggregates/gateway-customer/gateway-customer';
import { Inject, Optional, Lifecycle, Repository, DataSource } from '@heronjs/common';
import {
    IDatabase,
    QueryInput,
    BaseRepository,
    QueryInputFindOne,
    RepositoryOptions,
} from '@cbidigital/aqua-ddd';
import { GatewayCustomerDto, IGatewayCustomerMapper } from '@features/payment/domain';

@Repository({
    token: PAYMENT_MODULE_INJECT_TOKENS.REPOSITORY.GATEWAY_CUSTOMER,
    scope: Lifecycle.Singleton,
})
export class GatewayCustomerRepository extends BaseRepository<IGatewayCustomer> implements IGatewayCustomerRepository {
    constructor(
        @DataSource() protected readonly db: IDatabase,
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.DAO.GATEWAY_CUSTOMER)
        private readonly gatewayCustomerDao: IGatewayCustomerDao,
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.MAPPER.GATEWAY_CUSTOMER)
        private readonly mapper: IGatewayCustomerMapper,
    ) {
        super({ db });
    }

    async create(entity: IGatewayCustomer, options?: RepositoryOptions): Promise<IGatewayCustomer> {
        const trx = options?.trx ?? (await this.db.startTrx(options));
        const dto = await this.mapper.fromEntityToDto(entity);
        await this.gatewayCustomerDao.create(dto, options);
        if (!options?.trx) await trx.commit();

        return entity;
    }

    async update(entity: IGatewayCustomer, options?: RepositoryOptions): Promise<IGatewayCustomer> {
        const trx = options?.trx ?? (await this.db.startTrx(options));
        const dto = await this.mapper.fromEntityToDto(entity);
        await this.gatewayCustomerDao.update(dto, { ...options, trx });
        if (!options?.trx) await trx.commit();

        return entity;
    }

    async delete(entity: IGatewayCustomer, options?: RepositoryOptions): Promise<IGatewayCustomer> {
        await this.withTransaction(async (trx) => {
            await this.gatewayCustomerDao.deleteById(entity.id, { ...options, trx });
            return entity;
        }, options);

        return entity;
    }

    async find(input: QueryInput, options?: RepositoryOptions): Promise<IGatewayCustomer[]> {
        const dtos = await this.gatewayCustomerDao.find(input, options);
        const entities = await this.mapper.fromDtosToEntities(dtos as GatewayCustomerDto[]);
        return entities;
    }

    async findOne(input: QueryInputFindOne, options?: RepositoryOptions): Promise<Optional<IGatewayCustomer>> {
        const dto = await this.gatewayCustomerDao.findOne(input, options);
        const entity = dto ? await this.mapper.fromDtoToEntity(dto as GatewayCustomerDto) : dto;
        return entity;
    }

    async upsertList(entities: IGatewayCustomer[], options: RepositoryOptions) {
        const dtos = await this.mapper.fromEntitiesToDtos(entities);
        return entities;
    }

    async count(input: Pick<QueryInput, 'filter'>, options?: RepositoryOptions): Promise<number> {
        return this.gatewayCustomerDao.count(input, options);
    }
}
