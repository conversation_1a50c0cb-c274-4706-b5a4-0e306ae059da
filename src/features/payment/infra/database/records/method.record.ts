import { Nullable } from '@heronjs/common';
import { GatewayCodesEnum } from '@features/payment/domain/aggregates/gateway/enums';
import { MethodStatusEnum } from '@features/payment/domain/aggregates/method/enums';

export type MethodRecord = {
    code: string;
    gateway: GatewayCodesEnum;
    label: string;
    desc: Nullable<string>;
    status: MethodStatusEnum;
    visibility: boolean;
    sort_order: number;
    platform_exclusion: Nullable<any>;
    source_type_exclusion: Nullable<any>;
    created_at: Date;
    updated_at: Nullable<Date>;
};
