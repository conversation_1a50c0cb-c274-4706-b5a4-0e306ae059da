import { IRecordMapper } from '@cbidigital/aqua-ddd';
import { GatewayDto } from '@features/payment/domain';
import { GatewayRecord } from '@features/payment/infra/database/records';

export class GatewayRecordMapper implements IRecordMapper<GatewayDto, GatewayRecord> {
    fromRecordToDto(record: GatewayRecord): GatewayDto {
        return {
            code: record.code,
            label: record.label,
            desc: record.desc,
            status: record.status,
            createdAt: record.created_at,
            updatedAt: record.updated_at,
        };
    }

    fromDtoToRecord(dto: GatewayDto): GatewayRecord {
        return {
            code: dto.code,
            label: dto.label,
            desc: dto.desc,
            status: dto.status,
            created_at: dto.createdAt,
            updated_at: dto.updatedAt,
        };
    }

    fromRecordsToDtos(records: GatewayRecord[]): GatewayDto[] {
        return records.map((record) => this.fromRecordToDto(record));
    }

    fromDtosToRecords(dtos: GatewayDto[]): GatewayRecord[] {
        return dtos.map((dto) => this.fromDtoToRecord(dto));
    }
}
