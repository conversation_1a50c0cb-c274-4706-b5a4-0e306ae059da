import { IRecordMapper } from '@cbidigital/aqua-ddd';
import { GatewayCustomerDto } from '@features/payment/domain';
import { GatewayCustomerRecord } from '@features/payment/infra/database/records';

export class GatewayCustomerRecordMapper implements IRecordMapper<GatewayCustomerDto, GatewayCustomerRecord> {
    fromRecordToDto(record: GatewayCustomerRecord): GatewayCustomerDto {
        return {
            id: record.id,
            gateway: record.gateway,
            userId: record.user_id,
            gatewayCustomerId: record.gateway_customer_id,
            createdAt: record.created_at,
            updatedAt: record.updated_at,
        };
    }

    fromDtoToRecord(dto: GatewayCustomerDto): GatewayCustomerRecord {
        return {
            id: dto.id,
            gateway: dto.gateway,
            user_id: dto.userId,
            gateway_customer_id: dto.gatewayCustomerId,
            created_at: dto.createdAt,
            updated_at: dto.updatedAt,
        };
    }

    fromRecordsToDtos(records: GatewayCustomerRecord[]): GatewayCustomerDto[] {
        return records.map((record) => this.fromRecordToDto(record));
    }

    fromDtosToRecords(dtos: GatewayCustomerDto[]): GatewayCustomerRecord[] {
        return dtos.map((dto) => this.fromDtoToRecord(dto));
    }
}
