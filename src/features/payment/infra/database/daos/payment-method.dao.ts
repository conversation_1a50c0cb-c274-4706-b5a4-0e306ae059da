import { PaymentMethodDto } from '@features/payment/domain';
import { PAYMENT_MODULE_INJECT_TOKENS, TableNames } from 'src/shared';
import { Dao, DataSource, Lifecycle, Logger } from '@heronjs/common';
import { PaymentMethodRecord } from '@features/payment/infra/database/records';
import { PaymentMethodRecordMapper } from '@features/payment/infra/database/record-mappers';
import { BaseDao, IBaseDao, IDatabase, RepositoryOptions } from '@cbidigital/aqua-ddd';

export interface IPaymentMethodDao extends IBaseDao<PaymentMethodDto, PaymentMethodRecord> {
    update(
        entity: Partial<PaymentMethodDto>,
        options?: RepositoryOptions,
    ): Promise<Partial<PaymentMethodDto>>;
    findByUserId(userId: string, options?: RepositoryOptions): Promise<PaymentMethodDto[]>;
    findActiveByUserId(userId: string, options?: RepositoryOptions): Promise<PaymentMethodDto[]>;
    findDefaultByUserId(userId: string, options?: RepositoryOptions): Promise<PaymentMethodDto | null>;
}

@Dao({
    token: PAYMENT_MODULE_INJECT_TOKENS.DAO.PAYMENT_METHOD,
    scope: Lifecycle.Singleton,
})
export class PaymentMethodDao
    extends BaseDao<PaymentMethodDto, PaymentMethodRecord>
    implements IPaymentMethodDao
{
    private readonly logger = new Logger(this.constructor.name);

    constructor(@DataSource() db: IDatabase) {
        super({
            db,
            tableName: TableNames.PAYMENT_METHOD,
            recordMapper: new PaymentMethodRecordMapper(),
        });
    }

    async update(
        dto: Partial<PaymentMethodDto>,
        options: RepositoryOptions = {},
    ): Promise<Partial<PaymentMethodDto>> {
        const client = this.db.getClient();
        const record = this.recordMapper.fromDtoToRecord(dto as PaymentMethodDto);
        const query = client.table(this.tableName).where('id', dto.id!).update(record);
        if (options.trx) query.transacting(options.trx);
        await query;
        return dto;
    }

    async findByUserId(userId: string, options: RepositoryOptions = {}): Promise<PaymentMethodDto[]> {
        const client = this.db.getClient();
        const query = client.table(this.tableName).where('user_id', userId);
        if (options.trx) query.transacting(options.trx);
        const records = await query;
        return this.recordMapper.fromRecordsToDtos(records as PaymentMethodRecord[]) as PaymentMethodDto[];
    }

    async findActiveByUserId(userId: string, options: RepositoryOptions = {}): Promise<PaymentMethodDto[]> {
        const client = this.db.getClient();
        const query = client.table(this.tableName).where('user_id', userId).where('archived', false);
        if (options.trx) query.transacting(options.trx);
        const records = await query;
        return this.recordMapper.fromRecordsToDtos(records as PaymentMethodRecord[]) as PaymentMethodDto[];
    }

    async findDefaultByUserId(
        userId: string,
        options: RepositoryOptions = {},
    ): Promise<PaymentMethodDto | null> {
        const client = this.db.getClient();
        const query = client
            .table(this.tableName)
            .where('user_id', userId)
            .where('is_default', true)
            .where('archived', false)
            .first();
        if (options.trx) query.transacting(options.trx);
        const record = await query;
        return record
            ? (this.recordMapper.fromRecordToDto(record as PaymentMethodRecord) as PaymentMethodDto)
            : null;
    }
}
