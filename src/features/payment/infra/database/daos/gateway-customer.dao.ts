import { GatewayCustomerDto } from '@features/payment/domain';
import { PAYMENT_MODULE_INJECT_TOKENS, TableNames } from 'src/shared';
import { Dao, DataSource, Lifecycle, Logger } from '@heronjs/common';
import { GatewayCustomerRecord } from '@features/payment/infra/database/records';
import { GatewayCustomerRecordMapper } from '@features/payment/infra/database/record-mappers';
import { BaseDao, IBaseDao, IDatabase, RepositoryOptions } from '@cbidigital/aqua-ddd';

export interface IGatewayCustomerDao extends IBaseDao<GatewayCustomerDto, GatewayCustomerRecord> {
    update(
        entity: Partial<GatewayCustomerDto>,
        options?: RepositoryOptions,
    ): Promise<Partial<GatewayCustomerDto>>;
    findByUserId(userId: string, options?: RepositoryOptions): Promise<GatewayCustomerDto[]>;
    findByGatewayAndUserId(
        gateway: string,
        userId: string,
        options?: RepositoryOptions,
    ): Promise<GatewayCustomerDto | null>;
}

@Dao({
    token: PAYMENT_MODULE_INJECT_TOKENS.DAO.GATEWAY_CUSTOMER,
    scope: Lifecycle.Singleton,
})
export class GatewayCustomerDao
    extends BaseDao<GatewayCustomerDto, GatewayCustomerRecord>
    implements IGatewayCustomerDao
{
    private readonly logger = new Logger(this.constructor.name);

    constructor(@DataSource() db: IDatabase) {
        super({
            db,
            tableName: TableNames.GATEWAY_CUSTOMER,
            recordMapper: new GatewayCustomerRecordMapper(),
        });
    }

    async update(
        dto: Partial<GatewayCustomerDto>,
        options: RepositoryOptions = {},
    ): Promise<Partial<GatewayCustomerDto>> {
        const client = this.db.getClient();
        const record = this.recordMapper.fromDtoToRecord(dto as GatewayCustomerDto);
        const query = client.table(this.tableName).where('id', dto.id!).update(record);
        if (options.trx) query.transacting(options.trx);
        await query;
        return dto;
    }

    async findByUserId(userId: string, options: RepositoryOptions = {}): Promise<GatewayCustomerDto[]> {
        const client = this.db.getClient();
        const query = client.table(this.tableName).where('user_id', userId);
        if (options.trx) query.transacting(options.trx);
        const records = await query;
        return this.recordMapper.fromRecordsToDtos(
            records as GatewayCustomerRecord[],
        ) as GatewayCustomerDto[];
    }

    async findByGatewayAndUserId(
        gateway: string,
        userId: string,
        options: RepositoryOptions = {},
    ): Promise<GatewayCustomerDto | null> {
        const client = this.db.getClient();
        const query = client.table(this.tableName).where('gateway', gateway).where('user_id', userId).first();
        if (options.trx) query.transacting(options.trx);
        const record = await query;
        return record
            ? (this.recordMapper.fromRecordToDto(record as GatewayCustomerRecord) as GatewayCustomerDto)
            : null;
    }
}
