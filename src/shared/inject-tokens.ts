export const PAYMENT_MODULE_INJECT_TOKENS = {
    REPOSITORY: {
        GATEWAY: Symbol('GATEWAY_REPOSITORY').toString(),
        GATEWAY_CONFIG: Symbol('GATEWAY_CONFIG_REPOSITORY').toString(),
        GATEWAY_CUSTOMER: Symbol('GATEWAY_CUSTOMER_REPOSITORY').toString(),
        METHOD: Symbol('METHOD_REPOSITORY').toString(),
        PAYMENT_METHOD: Symbol('PAYMENT_METHOD_REPOSITORY').toString(),
        COUPON: Symbol('COUPON_REPOSITORY').toString(),
        TRANSACTION: Symbol('TRANSACTION_REPOSITORY').toString(),
        WEBHOOK_EVENT: Symbol('WEBHOOK_EVENT_REPOSITORY').toString(),
    },
    BUILDER: {
        GATEWAY: Symbol('GATEWAY_BUILDER').toString(),
        GATEWAY_CONFIG: Symbol('GATEWAY_CONFIG_BUILDER').toString(),
        GATEWAY_CUSTOMER: Symbol('GATEWAY_CUSTOMER_BUILDER').toString(),
        METHOD: Symbol('METHOD_BUILDER').toString(),
        PAYMENT_METHOD: Symbol('PAYMENT_METHOD_BUILDER').toString(),
        COUPON: Symbol('COUPON_BUILDER').toString(),
        TRANSACTION: Symbol('TRANSACTION_BUILDER').toString(),
        WEBHOOK_EVENT: Symbol('WEBHOOK_EVENT_BUILDER').toString(),
    },
    USECASE: {
        // Gateway
        CREATE_GATEWAY: Symbol('CREATE_GATEWAY_USECASE').toString(),
        // Coupon
        CREATE_COUPON: Symbol('CREATE_COUPON_USECASE').toString(),
        ADD_GATEWAY_COUPON: Symbol('ADD_GATEWAY_COUPON_USECASE').toString(),
        GET_COUPON_BY_CODE: Symbol('GET_COUPON_BY_CODE_USECASE').toString(),
    },
    DAO: {
        GATEWAY: Symbol('GATEWAY_DAO').toString(),
        GATEWAY_CONFIG: Symbol('GATEWAY_CONFIG_DAO').toString(),
        GATEWAY_CUSTOMER: Symbol('GATEWAY_CUSTOMER_DAO').toString(),
        METHOD: Symbol('METHOD_DAO').toString(),
        PAYMENT_METHOD: Symbol('PAYMENT_METHOD_DAO').toString(),
        COUPON: Symbol('COUPON_DAO').toString(),
        TRANSACTION: Symbol('TRANSACTION_DAO').toString(),
        WEBHOOK_EVENT: Symbol('WEBHOOK_EVENT_DAO').toString(),
    },
    MAPPER: {
        GATEWAY: Symbol('GATEWAY_MAPPER').toString(),
        GATEWAY_CONFIG: Symbol('GATEWAY_CONFIG_MAPPER').toString(),
        GATEWAY_CUSTOMER: Symbol('GATEWAY_CUSTOMER_MAPPER').toString(),
        METHOD: Symbol('METHOD_MAPPER').toString(),
        PAYMENT_METHOD: Symbol('PAYMENT_METHOD_MAPPER').toString(),
        COUPON: Symbol('COUPON_MAPPER').toString(),
        TRANSACTION: Symbol('TRANSACTION_MAPPER').toString(),
        WEBHOOK_EVENT: Symbol('WEBHOOK_EVENT_MAPPER').toString(),
    },
    SERVICE: {},
    FACTORY: {
        PAYMENT_GATEWAY: Symbol('PAYMENT_GATEWAY_FACTORY').toString(),
    },
    UTIL: {
        RETRY: Symbol('RETRY_UTIL').toString(),
        DATABASE: Symbol('DATABASE_UTIL').toString(),
    },
};
