{"name": "@cbidigital/payment-module", "version": "1.0.0", "description": "Payment module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "node build.js && tsc --build tsconfig.json", "debug": "nodemon --env=development --config nodemon.debug.json", "bdev": "node build.js && tsc --build tsconfig.json", "start": "node dist/index.js", "start:dev": "nodemon --env=development --config nodemon.debug.json", "migrate:make": "knex migrate:make", "migrate:up": "knex migrate:up", "migrate:latest": "knex migrate:latest", "migrate:down": "knex migrate:down", "migrate:rollback": "knex migrate:rollback", "migrate:list": "knex migrate:list", "seed:make": "knex seed:make", "seed:run": "knex seed:run", "test": "jest --updateSnapshot --detect<PERSON><PERSON>Handles", "copy": "cp -r migrations/ dist/migrations && cp package.json dist/ && cp README.md dist/ && cp .npmrc dist/", "lint": "eslint --ext .js,.ts .", "lint:fix": "eslint --fix --ext .js,.ts .", "lint:format": "prettier --write .", "package": "npm run lint && npm run build && node module.js"}, "keywords": [], "dependencies": {"@cbidigital/aqua-ddd": "^1.0.0-rc.9", "@heronjs/common": "3.3.40", "@heronjs/core": "3.4.40", "@heronjs/express": "3.1.7", "knex": "^3.1.0", "moment": "^2.30.1", "pg": "^8.16.0", "zod": "^3.25.49"}, "devDependencies": {"@types/node": "^22.15.29", "fs-extra": "^11.3.0", "ts-node": "^10.9.2"}}